# Amazon2FacebookPoster

Un outil automatisé pour extraire les produits en promotion d'Amazon et générer des posts Facebook optimisés pour l'affiliation.

## 🚀 Fonctionnalités principales

### ⚡ Scraping direct optimisé (NOUVEAU)
- **Sélecteurs CSS spécifiques** - Utilise les sélecteurs identifiés pour Amazon
- **Plus rapide et fiable** - Extraction directe sans IA
- **Fallback IA optionnel** - Bascule vers l'IA si le scraping direct échoue
- **Sélecteurs ciblés** :
  - `div[class*="ProductCard-module__card"][data-asin]` pour les cartes produits
  - `a[data-testid="product-card-link"]` pour les liens produits
  - `div[class*="style_badgeContainer"]` pour les réductions

### 🤖 Scraping basé sur l'IA
- **Résistant aux changements HTML** - Plus besoin de mettre à jour les sélecteurs CSS
- **Extraction intelligente** avec Gemini AI
- **Adaptation automatique** aux nouvelles structures Amazon

### 🔧 Scraping traditionnel
- **Extraction automatique** des produits en promotion depuis Amazon Deals
- **Support multi-pages** avec pagination automatique
- **Sélecteurs CSS** optimisés pour Amazon

### 📝 Génération de posts
- **Posts Facebook optimisés** avec l'IA (Gemini)
- **Liens d'affiliation** automatiques Amazon Associates
- **Format professionnel** avec emojis et hashtags

### 💾 Gestion des données
- **Sauvegarde JSON** des produits extraits
- **Mode headless** pour l'exécution en arrière-plan
- **Configuration flexible**

## 📋 Prérequis

- .NET 9.0 ou supérieur
- Clé API Google Gemini
- Tag Amazon Associates (optionnel)
- Chrome/Chromium installé

## ⚙️ Installation

1. Clonez le repository
2. Installez les dépendances :
   ```bash
   dotnet restore
   ```
3. Compilez le projet :
   ```bash
   dotnet build
   ```

## 🎯 Utilisation

### Démarrage

```bash
dotnet run
```

### Menu principal

1. **🔍 Mode complet** - Extrait tous les produits (plusieurs pages)
2. **🧪 Test rapide** - Une seule page pour tester
3. **👀 Aperçu** - Extraction sans génération de posts
4. **📂 Depuis JSON** - Génère des posts depuis un fichier existant
5. **🤖 Scraping IA** - Nouvelle approche résistante aux changements
6. **🚀 Processus COMPLET IA** - **NOUVEAU** : Workflow automatisé complet
7. **⚙️ Configuration** - Options avancées
8. **❌ Quitter**

### 🌟 Approche recommandée : Processus COMPLET IA

L'option **6. Processus COMPLET IA** est la solution la plus avancée :
- ✅ **Workflow automatisé** : Scraping → Détails → Affiliation → Posts
- ✅ **Détection intelligente** : Pagination traditionnelle vs infinite scroll
- ✅ **Scroll complet** : Chargement de tout le contenu des pages produits
- ✅ **Vrais liens d'affiliation** : Clic sur "Obtenir le lien" Amazon Associates
- ✅ **Délais humains** : Simulation réaliste (1-20s) pour éviter la détection
- ✅ **Extraction complète** : Prix, avis, images, disponibilité
- ✅ **Posts optimisés** : IA Gemini pour des posts Facebook parfaits
- ✅ **Statistiques détaillées** : Monitoring en temps réel avec gestion d'erreurs

## 📁 Structure du projet

### Fichiers principaux
- `OptimizedProgram.cs` - **🎯 Interface utilisateur optimisée**
- `UnifiedAmazonProcessor.cs` - **🏗️ Processeur unifié avec stratégies**
- `IExtractionStrategy.cs` - **⚡ Stratégies d'extraction (Direct/AI/Hybrid/Configurable)**
- `ProductDetailExtractor.cs` - **🔍 Extraction complète avec scroll**
- `OptimizedAmazonLoader.cs` - **🌐 Gestion optimisée du navigateur**
- `OptimizedFacebookPostGenerator.cs` - **📝 Génération améliorée des posts**
- `ConfigurableExtractionEngine.cs` - **🔧 Moteur d'extraction configurable**
- `Configuration.cs` - **⚙️ Configuration unifiée et validée**

### Modèles de données
- `ProductInfo.cs` - Structure optimisée des produits
- `Configuration.cs` - Configuration centralisée (AmazonScrapingConfiguration)

## 🔧 Configuration

### Clé API Gemini
1. Obtenez votre clé sur [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Entrez-la lors du premier démarrage du programme

### Tag Amazon Associates
1. Inscrivez-vous sur [Amazon Associates](https://partenaires.amazon.fr/)
2. Utilisez votre tag lors de l'extraction (ex: `votre-tag-20`)

## 📊 Formats de sortie

### JSON des produits (Scraping IA)
```json
{
  "Title": "Nom complet du produit",
  "ProductUrl": "https://amazon.fr/dp/ASIN",
  "Discount": "-25%",
  "AffiliateLink": "https://amazon.fr/dp/ASIN?tag=votre-tag-20",
  "IsDeal": true
}
```

### Posts Facebook générés
```text
💥 Économisez 25% DÈS MAINTENANT sur [Nom du produit]! 💥
PRIX EXCEPTIONNEL : 29,99 € au lieu de 39,99 € !
👉 Ne manquez pas cette offre : [lien d'affiliation]

⭐ Note : 4,5/5 (1,234 avis)
🚚 Livraison gratuite
📦 Retours gratuits

#Amazon #BonPlan #Promo #Deal #Shopping

En tant que partenaire Amazon, les liens #Amazon sont rémunérés
```

## 🛠️ Dépannage

### Problèmes courants

1. **Aucun produit extrait (Scraping traditionnel)**
   - ✅ **Solution** : Utilisez le **Scraping IA** (option 5)
   - Amazon change régulièrement sa structure HTML

2. **Erreur API Gemini**
   - Vérifiez votre clé API
   - Vérifiez vos quotas sur Google AI Studio

3. **Erreur de navigation**
   - Vérifiez que Chrome est installé
   - Essayez le mode non-headless pour diagnostiquer

### Avantages du Scraping IA

| Traditionnel | IA |
|-------------|-----|
| ❌ Sensible aux changements HTML | ✅ Résistant aux changements |
| ❌ Maintenance des sélecteurs | ✅ Pas de maintenance |
| ❌ Extraction parfois incomplète | ✅ Extraction intelligente |
| ✅ Plus rapide | ⚠️ Légèrement plus lent |

## 📈 Performances

- **Scraping traditionnel** : ~10-20 produits/minute
- **Scraping IA** : ~5-10 produits/minute (mais plus fiable)
- **Génération de posts** : ~1 post/2 secondes

## 📝 Licence

MIT License - Voir le fichier LICENSE pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à ouvrir une issue ou une pull request.

## 🆕 Nouveautés

### Version actuelle
- ✅ **Scraping basé sur l'IA** - Résistant aux changements HTML
- ✅ **Interface simplifiée** - Menu nettoyé et optimisé
- ✅ **Meilleure fiabilité** - Extraction plus stable
- ✅ **Documentation mise à jour** - Guide complet
