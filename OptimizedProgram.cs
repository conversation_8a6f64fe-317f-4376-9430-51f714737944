namespace Amazon2FacebookPoster
{
    /// <summary>
    /// Optimized program interface with simplified menu and unified architecture
    /// </summary>
    public class OptimizedProgram
    {
        private static string? _geminiApiKey;
        private static string? _amazonAssociateTag;

        public static async Task Main(string[] args)
        {
            Console.WriteLine("🛒 Amazon2FacebookPoster - Optimized Edition");
            Console.WriteLine("=" + new string('=', 50));
            Console.WriteLine("🚀 Unified architecture with strategy-based extraction");
            Console.WriteLine();

            // Initialize configuration
            if (!await InitializeConfigurationAsync())
            {
                Console.WriteLine("❌ Configuration failed. Exiting...");
                return;
            }

            // Main menu loop
            while (true)
            {
                DisplayMainMenu();
                var choice = Console.ReadLine()?.Trim();

                try
                {
                    var shouldExit = await HandleMenuChoiceAsync(choice);
                    if (shouldExit) break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Error: {ex.Message}");
                    Console.WriteLine("Press any key to continue...");
                    Console.ReadKey();
                }
            }

            Console.WriteLine("👋 Thank you for using Amazon2FacebookPoster!");
        }

        /// <summary>
        /// Initialize API keys and configuration
        /// </summary>
        private static async Task<bool> InitializeConfigurationAsync()
        {
            Console.WriteLine("🔧 CONFIGURATION SETUP");
            Console.WriteLine("-" + new string('-', 30));

            // Get Gemini API key
            Console.Write("Enter your Gemini API key: ");
            _geminiApiKey = Console.ReadLine()?.Trim();

            if (string.IsNullOrWhiteSpace(_geminiApiKey))
            {
                Console.WriteLine("❌ Gemini API key is required!");
                Console.WriteLine("Get your key from: https://makersuite.google.com/app/apikey");
                return false;
            }

            // Get Amazon Associate tag
            Console.Write("Enter your Amazon Associates tag (e.g., your-tag-20): ");
            _amazonAssociateTag = Console.ReadLine()?.Trim();

            if (string.IsNullOrWhiteSpace(_amazonAssociateTag))
            {
                _amazonAssociateTag = "default-tag-20";
                Console.WriteLine($"⚠️ Using default tag: {_amazonAssociateTag}");
            }

            Console.WriteLine("✅ Configuration completed!");
            Console.WriteLine();

            return true;
        }

        /// <summary>
        /// Display the main menu
        /// </summary>
        private static void DisplayMainMenu()
        {
            Console.Clear();
            Console.WriteLine("🛒 Amazon2FacebookPoster - Main Menu");
            Console.WriteLine("=" + new string('=', 40));
            Console.WriteLine();
            Console.WriteLine("🚀 MAIN OPERATIONS:");
            Console.WriteLine("1. 🎯 Smart Extraction (Hybrid Strategy - Recommended)");
            Console.WriteLine("2. ⚡ Direct CSS Scraping (Fast & Reliable)");
            Console.WriteLine("3. 🤖 AI-Only Extraction (Comprehensive)");
            Console.WriteLine("4. 🔧 Configurable Extraction (External Rules)");
            Console.WriteLine();
            Console.WriteLine("🔧 UTILITIES:");
            Console.WriteLine("5. 🧪 Quick Test (1 page, 3 products)");
            Console.WriteLine("6. 👀 Preview Products (No posts generation)");
            Console.WriteLine("7. 📂 Process from JSON file");
            Console.WriteLine("8. ⚙️ Configuration Management Tools");
            Console.WriteLine();
            Console.WriteLine("9. ❌ Exit");
            Console.WriteLine();
            Console.Write("Choose an option (1-7): ");
        }

        /// <summary>
        /// Handle menu choice
        /// </summary>
        private static async Task<bool> HandleMenuChoiceAsync(string? choice)
        {
            return choice switch
            {
                "1" => await RunHybridExtractionAsync(),
                "2" => await RunDirectExtractionAsync(),
                "3" => await RunAIExtractionAsync(),
                "4" => await RunConfigurableExtractionAsync(),
                "5" => await RunQuickTestAsync(),
                "6" => await RunPreviewAsync(),
                "7" => await RunFromJsonAsync(),
                "8" => await RunConfigurationManagementAsync(),
                "9" => true, // Exit
                _ => await HandleInvalidChoiceAsync()
            };
        }

        /// <summary>
        /// Run hybrid extraction (recommended)
        /// </summary>
        private static async Task<bool> RunHybridExtractionAsync()
        {
            Console.Clear();
            Console.WriteLine("🎯 HYBRID EXTRACTION (RECOMMENDED)");
            Console.WriteLine("=" + new string('=', 40));
            Console.WriteLine("• Direct CSS scraping with AI fallback");
            Console.WriteLine("• Best balance of speed and accuracy");
            Console.WriteLine("• Handles Amazon layout changes automatically");
            Console.WriteLine();

            var config = await GetUserConfigurationAsync(ExtractionStrategy.Hybrid);
            if (config == null) return false;

            return await ExecuteProcessingAsync(config);
        }

        /// <summary>
        /// Run direct CSS extraction
        /// </summary>
        private static async Task<bool> RunDirectExtractionAsync()
        {
            Console.Clear();
            Console.WriteLine("⚡ DIRECT CSS EXTRACTION");
            Console.WriteLine("=" + new string('=', 30));
            Console.WriteLine("• Fast CSS selector-based scraping");
            Console.WriteLine("• Most efficient for current Amazon layout");
            Console.WriteLine("• May need updates if Amazon changes structure");
            Console.WriteLine();

            var config = await GetUserConfigurationAsync(ExtractionStrategy.Direct);
            if (config == null) return false;

            return await ExecuteProcessingAsync(config);
        }

        /// <summary>
        /// Run AI-only extraction
        /// </summary>
        private static async Task<bool> RunAIExtractionAsync()
        {
            Console.Clear();
            Console.WriteLine("🤖 AI-ONLY EXTRACTION");
            Console.WriteLine("=" + new string('=', 25));
            Console.WriteLine("• Comprehensive AI-based analysis");
            Console.WriteLine("• Adapts to any Amazon layout changes");
            Console.WriteLine("• Slower but most thorough");
            Console.WriteLine();

            var config = await GetUserConfigurationAsync(ExtractionStrategy.AI);
            if (config == null) return false;

            return await ExecuteProcessingAsync(config);
        }

        /// <summary>
        /// Run configurable extraction
        /// </summary>
        private static async Task<bool> RunConfigurableExtractionAsync()
        {
            Console.Clear();
            Console.WriteLine("🔧 CONFIGURABLE EXTRACTION");
            Console.WriteLine("=" + new string('=', 30));
            Console.WriteLine("• Uses external configuration rules");
            Console.WriteLine("• Easily customizable without code changes");
            Console.WriteLine("• Hot-reload configuration support");
            Console.WriteLine("• Perfect for non-technical users");
            Console.WriteLine();

            var config = await GetUserConfigurationAsync(ExtractionStrategy.Configurable);
            if (config == null) return false;

            return await ExecuteProcessingAsync(config);
        }

        /// <summary>
        /// Run configuration management tools
        /// </summary>
        private static async Task<bool> RunConfigurationManagementAsync()
        {
            Console.Clear();
            Console.WriteLine("⚙️ CONFIGURATION MANAGEMENT");
            Console.WriteLine("=" + new string('=', 30));
            Console.WriteLine("• Manage extraction rules");
            Console.WriteLine("• Test selectors on live pages");
            Console.WriteLine("• Validate configurations");
            Console.WriteLine("• Create and edit rules");
            Console.WriteLine();

            try
            {
                var tools = new ConfigurationManagementTools();
                await tools.RunConfigurationEditorAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Configuration management failed: {ex.Message}");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
            }

            return false;
        }

        /// <summary>
        /// Get user configuration for processing
        /// </summary>
        private static async Task<AmazonScrapingConfiguration?> GetUserConfigurationAsync(ExtractionStrategy strategy)
        {
            try
            {
                Console.Write("Number of pages to process (1-10, recommended: 3-5): ");
                var pagesInput = Console.ReadLine();
                var maxPages = int.TryParse(pagesInput, out var pages) ? Math.Clamp(pages, 1, 10) : 3;

                Console.Write("Headless mode? (y/n, recommended: n to see progress): ");
                var headlessInput = Console.ReadLine()?.ToLower();
                var headless = headlessInput == "y" || headlessInput == "yes";

                Console.Write("Generate Facebook posts? (y/n, recommended: y): ");
                var postsInput = Console.ReadLine()?.ToLower();
                var generatePosts = postsInput != "n" && postsInput != "no";

                var config = AmazonScrapingConfiguration.CreateCustomConfiguration(
                    _geminiApiKey!,
                    _amazonAssociateTag!,
                    strategy,
                    maxPages,
                    headless,
                    generatePosts);

                var validation = config.Validate();
                if (validation != System.ComponentModel.DataAnnotations.ValidationResult.Success)
                {
                    Console.WriteLine($"❌ Configuration error: {validation.ErrorMessage}");
                    return null;
                }

                return config;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Configuration error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Execute the processing with progress reporting
        /// </summary>
        private static async Task<bool> ExecuteProcessingAsync(AmazonScrapingConfiguration config)
        {
            Console.WriteLine();
            Console.WriteLine("🚀 Starting processing...");
            Console.WriteLine($"📊 Strategy: {config.ExtractionStrategy}");
            Console.WriteLine($"📄 Pages: {config.MaxPages}");
            Console.WriteLine($"📁 Output: {config.OutputDirectory}");
            Console.WriteLine();

            using var processor = new UnifiedAmazonProcessor(config);
            var result = await processor.ProcessAsync();

            Console.WriteLine();
            Console.WriteLine(result.GetSummaryReport());

            if (result.Success)
            {
                Console.WriteLine($"📁 Check output directory: {result.OutputDirectory}");
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Quick test with minimal configuration
        /// </summary>
        private static async Task<bool> RunQuickTestAsync()
        {
            Console.Clear();
            Console.WriteLine("🧪 QUICK TEST MODE");
            Console.WriteLine("=" + new string('=', 20));
            Console.WriteLine("• 1 page, limited products");
            Console.WriteLine("• Fast validation of setup");
            Console.WriteLine();

            using var processor = UnifiedAmazonProcessor.CreateForTesting(_geminiApiKey!, _amazonAssociateTag!);
            var result = await processor.ProcessSinglePageAsync();

            Console.WriteLine();
            Console.WriteLine(result.GetSummaryReport());
            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Preview products without generating posts
        /// </summary>
        private static async Task<bool> RunPreviewAsync()
        {
            Console.Clear();
            Console.WriteLine("👀 PREVIEW MODE");
            Console.WriteLine("=" + new string('=', 15));

            var config = AmazonScrapingConfiguration.CreateTestConfiguration(_geminiApiKey!, _amazonAssociateTag!);
            config.GeneratePosts = false;

            using var processor = new UnifiedAmazonProcessor(config);
            var products = await processor.PreviewProductsAsync();

            Console.WriteLine($"📊 Found {products.Count} products");
            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Process from existing JSON file
        /// </summary>
        private static async Task<bool> RunFromJsonAsync()
        {
            Console.Clear();
            Console.WriteLine("📂 PROCESS FROM JSON");
            Console.WriteLine("=" + new string('=', 20));

            Console.Write("Enter JSON file path: ");
            var jsonPath = Console.ReadLine()?.Trim();

            if (string.IsNullOrEmpty(jsonPath) || !File.Exists(jsonPath))
            {
                Console.WriteLine("❌ File not found!");
                Console.WriteLine("Press any key to return to menu...");
                Console.ReadKey();
                return false;
            }

            var config = AmazonScrapingConfiguration.CreateCustomConfiguration(
                _geminiApiKey!,
                _amazonAssociateTag!,
                ExtractionStrategy.Hybrid, // Default strategy for JSON processing
                1, // Single page since we're processing from JSON
                false, // Not headless for JSON processing
                true); // Generate posts

            // Override output directory for JSON processing
            config.OutputDirectory = $"json_output_{DateTime.Now:yyyyMMdd_HHmmss}";

            using var processor = new UnifiedAmazonProcessor(config);
            var result = await processor.ProcessFromJsonAsync(jsonPath);

            Console.WriteLine();
            Console.WriteLine(result.GetSummaryReport());
            Console.WriteLine();
            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            return false;
        }

        /// <summary>
        /// Handle invalid menu choice
        /// </summary>
        private static async Task<bool> HandleInvalidChoiceAsync()
        {
            Console.WriteLine("❌ Invalid option. Please choose 1-9.");
            await Task.Delay(1500);
            return false;
        }
    }
}
